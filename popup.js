// popup.js
console.log('🚀 Popup script loaded');

document.addEventListener('DOMContentLoaded', () => {
  console.log('📄 Popup DOM loaded');

  const fullBtn = document.getElementById('capture-full');
  const regionBtn = document.getElementById('capture-region');

  if (!fullBtn || !regionBtn) {
    console.error('❌ Buttons not found in popup');
    return;
  }

  fullBtn.addEventListener('click', () => {
    console.log('🖼️ Full screenshot clicked');
    chrome.runtime.sendMessage({ action: "captureVisibleTab" }, (response) => {
      // Open the editor with the full page screenshot
      chrome.runtime.sendMessage({ action: "openEditor", dataUrl: response.dataUrl });
      window.close();
    });
  });

  regionBtn.addEventListener('click', () => {
    console.log('✂️ Region screenshot clicked');
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      if (!tabs || tabs.length === 0) {
        console.error('❌ No active tab found');
        alert('无法找到当前标签页');
        return;
      }

      const tabId = tabs[0].id;
      console.log('📍 Injecting content script into tab:', tabId);
      console.log('🌐 Tab URL:', tabs[0].url);

      // 检查是否是特殊页面
      if (tabs[0].url.startsWith('chrome://') ||
          tabs[0].url.startsWith('chrome-extension://') ||
          tabs[0].url.startsWith('edge://') ||
          tabs[0].url.startsWith('about:')) {
        alert('无法在此类系统页面使用截图功能，请在普通网页中使用');
        return;
      }

      chrome.scripting.executeScript({
        target: { tabId: tabId },
        files: ['content.js']
      }, (result) => {
        if (chrome.runtime.lastError) {
          console.error('❌ Script injection failed:', chrome.runtime.lastError);
          alert('无法在此页面使用截图功能：' + chrome.runtime.lastError.message);
        } else {
          console.log('✅ Content script injected successfully', result);
        }
      });
    });

    // 延迟关闭popup，给content script时间执行
    setTimeout(() => {
      window.close();
    }, 100);
  });
});

// The saveImage function and the listener for 'saveRegion' are no longer needed here
// as all saving is handled by the editor.