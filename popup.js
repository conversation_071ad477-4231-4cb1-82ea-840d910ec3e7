// popup.js
document.getElementById('capture-full').addEventListener('click', () => {
  chrome.runtime.sendMessage({ action: "captureVisibleTab" }, (response) => {
    // Open the editor with the full page screenshot
    chrome.runtime.sendMessage({ action: "openEditor", dataUrl: response.dataUrl });
    window.close();
  });
});

document.getElementById('capture-region').addEventListener('click', () => {
  chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
    chrome.scripting.executeScript({
      target: { tabId: tabs[0].id },
      files: ['content.js']
    });
  });
  window.close();
});

// The saveImage function and the listener for 'saveRegion' are no longer needed here
// as all saving is handled by the editor.