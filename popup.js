// popup.js
document.getElementById('capture-full').addEventListener('click', () => {
  chrome.runtime.sendMessage({ action: "captureVisibleTab" }, (response) => {
    // Open the editor with the full page screenshot
    chrome.runtime.sendMessage({ action: "openEditor", dataUrl: response.dataUrl });
    window.close();
  });
});

document.getElementById('capture-region').addEventListener('click', () => {
  chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
    console.log('Injecting content script into tab:', tabs[0].id);
    chrome.scripting.executeScript({
      target: { tabId: tabs[0].id },
      files: ['content.js']
    }, (result) => {
      if (chrome.runtime.lastError) {
        console.error('Script injection failed:', chrome.runtime.lastError);
        alert('无法在此页面使用截图功能：' + chrome.runtime.lastError.message);
      } else {
        console.log('Content script injected successfully');
      }
    });
  });
  window.close();
});

// The saveImage function and the listener for 'saveRegion' are no longer needed here
// as all saving is handled by the editor.