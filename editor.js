// editor.js
window.addEventListener('DOMContentLoaded', () => {
  const canvas = document.getElementById('editor-canvas');
  const ctx = canvas.getContext('2d');
  const saveBtn = document.getElementById('save-btn');
  const cancelBtn = document.getElementById('cancel-btn');
  const colorPicker = document.getElementById('color-picker');
  const lineWidth = document.getElementById('line-width');
  const arrowBtn = document.getElementById('tool-arrow');
  const textBtn = document.getElementById('tool-text');
  const rectBtn = document.getElementById('tool-rect');

  let drawing = false;
  let currentTool = 'arrow'; // 'arrow', 'text', 'rect', 'pencil'
  let originalImage = new Image();
  let selection = null;
  let isDragging = false;
  let isResizing = false;
  let isPanning = false;
  let dragOffset = { x: 0, y: 0 };
  let resizeHandle = null;
  let currentArrow = null;
  let currentPath = [];
  let annotations = [];
  let annotationHistory = [];
  let scale = 1;
  let panOffset = { x: 0, y: 0 };
  let spacePressed = false;
  const HANDLE_SIZE = 8;
  const MIN_SCALE = 0.1;
  const MAX_SCALE = 5;

  // Load the screenshot data from background script
  console.log('Editor loading...');
  chrome.runtime.sendMessage({ action: "getScreenshotData" }, (response) => {
    console.log('Screenshot data response:', response);
    if (response && response.dataUrl) {
      originalImage.onload = () => {
        console.log('Image loaded:', originalImage.width, 'x', originalImage.height);
        canvas.width = originalImage.width;
        canvas.height = originalImage.height;
        redrawCanvas();
      };
      originalImage.onerror = () => {
        console.error('Failed to load image');
        alert('图片加载失败！');
        window.close();
      };
      originalImage.src = response.dataUrl;
    } else {
      console.error('No screenshot data received');
      alert('无法加载截图数据！');
      window.close();
    }
  });

  function redrawCanvas() {
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    ctx.save();
    ctx.translate(panOffset.x, panOffset.y);
    ctx.scale(scale, scale);
    
    // Draw image
    ctx.drawImage(originalImage, 0, 0);
    
    // Draw all annotations
    annotations.forEach(ann => {
      if (ann.type === 'arrow') {
        drawArrow(ctx, ann.startX, ann.startY, ann.endX, ann.endY, ann.color, ann.lineWidth);
      } else if (ann.type === 'text') {
        ctx.font = ann.font;
        ctx.fillStyle = ann.color;
        ctx.fillText(ann.text, ann.x, ann.y);
      } else if (ann.type === 'rect') {
        ctx.strokeStyle = ann.color;
        ctx.lineWidth = ann.lineWidth;
        ctx.strokeRect(ann.x, ann.y, ann.width, ann.height);
      } else if (ann.type === 'pencil') {
        ctx.strokeStyle = ann.color;
        ctx.lineWidth = ann.lineWidth;
        ctx.lineCap = 'round';
        ctx.lineJoin = 'round';
        ctx.beginPath();
        if (ann.points.length > 0) {
          ctx.moveTo(ann.points[0].x, ann.points[0].y);
          for (let i = 1; i < ann.points.length; i++) {
            ctx.lineTo(ann.points[i].x, ann.points[i].y);
          }
        }
        ctx.stroke();
      }
    });

    // Draw selection box
    if (selection) {
      ctx.save();
      ctx.strokeStyle = '#007bff';
      ctx.lineWidth = 2 / scale; // Adjust line width for zoom
      ctx.setLineDash([5 / scale, 5 / scale]);
      ctx.strokeRect(selection.x, selection.y, selection.width, selection.height);
      
      // Draw resize handles
      ctx.setLineDash([]);
      ctx.fillStyle = '#007bff';
      const handleSize = HANDLE_SIZE / scale;
      const handles = [
        { x: selection.x - handleSize/2, y: selection.y - handleSize/2 },
        { x: selection.x + selection.width - handleSize/2, y: selection.y - handleSize/2 },
        { x: selection.x - handleSize/2, y: selection.y + selection.height - handleSize/2 },
        { x: selection.x + selection.width - handleSize/2, y: selection.y + selection.height - handleSize/2 }
      ];
      
      handles.forEach(handle => {
        ctx.fillRect(handle.x, handle.y, handleSize, handleSize);
      });
      ctx.restore();
    }
    
    ctx.restore();
  }

  function screenToCanvas(screenX, screenY) {
    const rect = canvas.getBoundingClientRect();
    
    // 计算相对于canvas左上角的坐标
    const x = screenX - rect.left;
    const y = screenY - rect.top;
    
    // 考虑canvas的缩放比例
    const scaleX = canvas.width / canvas.offsetWidth;
    const scaleY = canvas.height / canvas.offsetHeight;
    
    // 转换到canvas坐标系
    const canvasX = x * scaleX;
    const canvasY = y * scaleY;
    
    // 应用平移和缩放变换
    return {
      x: (canvasX - panOffset.x) / scale,
      y: (canvasY - panOffset.y) / scale
    };
  }

  function canvasToScreen(canvasX, canvasY) {
    return {
      x: canvasX * scale + panOffset.x,
      y: canvasY * scale + panOffset.y
    };
  }

  function drawArrow(ctx, startX, startY, endX, endY, color, lineWidth) {
    const angle = Math.atan2(endY - startY, endX - startX);
    const arrowLength = Math.min(lineWidth * 4, 20);
    const arrowAngle = Math.PI / 6;

    // 绘制主线
    ctx.beginPath();
    ctx.moveTo(startX, startY);
    ctx.lineTo(endX, endY);
    ctx.strokeStyle = color;
    ctx.lineWidth = lineWidth;
    ctx.stroke();

    // 绘制箭头头部
    ctx.beginPath();
    ctx.moveTo(endX, endY);
    ctx.lineTo(
      endX - arrowLength * Math.cos(angle - arrowAngle),
      endY - arrowLength * Math.sin(angle - arrowAngle)
    );
    ctx.moveTo(endX, endY);
    ctx.lineTo(
      endX - arrowLength * Math.cos(angle + arrowAngle),
      endY - arrowLength * Math.sin(angle + arrowAngle)
    );
    ctx.stroke();
  }

  function getHandleAt(x, y) {
    if (!selection) return null;
    
    const handles = [
      { name: 'nw', x: selection.x, y: selection.y },
      { name: 'ne', x: selection.x + selection.width, y: selection.y },
      { name: 'sw', x: selection.x, y: selection.y + selection.height },
      { name: 'se', x: selection.x + selection.width, y: selection.y + selection.height }
    ];
    
    for (let handle of handles) {
      if (x >= handle.x - HANDLE_SIZE/2 && x <= handle.x + HANDLE_SIZE/2 &&
          y >= handle.y - HANDLE_SIZE/2 && y <= handle.y + HANDLE_SIZE/2) {
        return handle;
      }
    }
    return null;
  }

  function isInsideSelection(x, y) {
    if (!selection) return false;
    return x >= selection.x && x <= selection.x + selection.width &&
           y >= selection.y && y <= selection.y + selection.height;
  }

  function getMousePos(e) {
    return screenToCanvas(e.clientX, e.clientY);
  }

  canvas.addEventListener('mousedown', (e) => {
    // 检查是否按住空格键进行平移
    if (spacePressed) {
      isPanning = true;
      dragOffset.x = e.clientX - panOffset.x;
      dragOffset.y = e.clientY - panOffset.y;
      canvas.style.cursor = 'grabbing';
      return;
    }

    const pos = getMousePos(e);

    if (currentTool === 'arrow') {
      drawing = true;
      currentArrow = { startX: pos.x, startY: pos.y, endX: pos.x, endY: pos.y };
    } else if (currentTool === 'pencil') {
      drawing = true;
      currentPath = [pos];
    } else if (currentTool === 'rect') {
      const handle = getHandleAt(pos.x, pos.y);
      if (handle) {
        isResizing = true;
        resizeHandle = handle;
        dragOffset.x = pos.x;
        dragOffset.y = pos.y;
      } else if (isInsideSelection(pos.x, pos.y)) {
        isDragging = true;
        dragOffset.x = pos.x - selection.x;
        dragOffset.y = pos.y - selection.y;
      } else {
        selection = { startX: pos.x, startY: pos.y, x: pos.x, y: pos.y, width: 0, height: 0 };
        drawing = true;
      }
    }
  });

  canvas.addEventListener('mousemove', (e) => {
    // 处理平移
    if (isPanning) {
      panOffset.x = e.clientX - dragOffset.x;
      panOffset.y = e.clientY - dragOffset.y;
      redrawCanvas();
      return;
    }

    const pos = getMousePos(e);

    if (drawing) {
      if (currentTool === 'arrow' && currentArrow) {
        currentArrow.endX = pos.x;
        currentArrow.endY = pos.y;
        redrawCanvas();

        // 绘制预览箭头
        ctx.save();
        ctx.translate(panOffset.x, panOffset.y);
        ctx.scale(scale, scale);
        drawArrow(ctx, currentArrow.startX, currentArrow.startY, currentArrow.endX, currentArrow.endY, colorPicker.value, lineWidth.value);
        ctx.restore();
      } else if (currentTool === 'pencil') {
        currentPath.push(pos);
        // 绘制实时线条
        if (currentPath.length > 1) {
          ctx.save();
          ctx.translate(panOffset.x, panOffset.y);
          ctx.scale(scale, scale);
          ctx.beginPath();
          ctx.moveTo(currentPath[currentPath.length-2].x, currentPath[currentPath.length-2].y);
          ctx.lineTo(pos.x, pos.y);
          ctx.strokeStyle = colorPicker.value;
          ctx.lineWidth = lineWidth.value;
          ctx.lineCap = 'round';
          ctx.stroke();
          ctx.restore();
        }
      } else if (currentTool === 'rect') {
        selection.x = Math.min(selection.startX, pos.x);
        selection.y = Math.min(selection.startY, pos.y);
        selection.width = Math.abs(pos.x - selection.startX);
        selection.height = Math.abs(pos.y - selection.startY);
        redrawCanvas();
      }
    } else if (isDragging && selection) {
      selection.x = pos.x - dragOffset.x;
      selection.y = pos.y - dragOffset.y;
      redrawCanvas();
    } else if (isResizing && selection && resizeHandle) {
      const dx = pos.x - dragOffset.x;
      const dy = pos.y - dragOffset.y;

      if (resizeHandle.name === 'nw') {
        selection.x += dx;
        selection.y += dy;
        selection.width -= dx;
        selection.height -= dy;
      } else if (resizeHandle.name === 'ne') {
        selection.y += dy;
        selection.width += dx;
        selection.height -= dy;
      } else if (resizeHandle.name === 'sw') {
        selection.x += dx;
        selection.width -= dx;
        selection.height += dy;
      } else if (resizeHandle.name === 'se') {
        selection.width += dx;
        selection.height += dy;
      }

      dragOffset.x = pos.x;
      dragOffset.y = pos.y;
      redrawCanvas();
    }
  });

  canvas.addEventListener('mouseup', () => {
    // 处理平移结束
    isPanning = false;
    if (spacePressed) {
      canvas.style.cursor = 'grab';
    } else {
      canvas.style.cursor = 'default';
    }

    let hasNewAnnotation = false;

    if (drawing) {
      if (currentTool === 'arrow' && currentArrow) {
        // 检查箭头是否有足够长度
        const dx = currentArrow.endX - currentArrow.startX;
        const dy = currentArrow.endY - currentArrow.startY;
        const length = Math.sqrt(dx * dx + dy * dy);

        if (length > 5) {
          annotations.push({
            type: 'arrow',
            startX: currentArrow.startX,
            startY: currentArrow.startY,
            endX: currentArrow.endX,
            endY: currentArrow.endY,
            color: colorPicker.value,
            lineWidth: lineWidth.value
          });
          hasNewAnnotation = true;
        }
      } else if (currentTool === 'pencil' && currentPath.length > 1) {
        annotations.push({
          type: 'pencil',
          points: [...currentPath],
          color: colorPicker.value,
          lineWidth: lineWidth.value
        });
        hasNewAnnotation = true;
      } else if (currentTool === 'rect' && selection && selection.width > 10 && selection.height > 10) {
        annotations.push({
          type: 'rect',
          x: selection.x,
          y: selection.y,
          width: selection.width,
          height: selection.height,
          color: colorPicker.value,
          lineWidth: lineWidth.value
        });
        hasNewAnnotation = true;
        selection = null;
      }
    }

    // Save history when new annotation is added
    if (hasNewAnnotation) {
      saveHistory();
    }

    drawing = false;
    isDragging = false;
    isResizing = false;
    resizeHandle = null;
    currentArrow = null;
    currentPath = [];
    redrawCanvas();
  });

  canvas.addEventListener('click', (e) => {
    if (currentTool === 'text') {
      const pos = getMousePos(e);
      const text = prompt('请输入文本内容：');
      if (text) {
        annotations.push({
          type: 'text',
          text: text,
          x: pos.x,
          y: pos.y,
          color: colorPicker.value,
          font: `${lineWidth.value * 4}px Arial`
        });
        saveHistory();
        redrawCanvas();
      }
    }
  });

  arrowBtn.addEventListener('click', () => currentTool = 'arrow');
  textBtn.addEventListener('click', () => currentTool = 'text');
  rectBtn.addEventListener('click', () => currentTool = 'rect');

  // Undo functionality
  const undoBtn = document.getElementById('undo-btn');
  
  function saveHistory() {
    annotationHistory.push([...annotations]);
    updateUndoButton();
  }
  
  function undo() {
    if (annotationHistory.length > 0) {
      annotations = annotationHistory.pop();
      updateUndoButton();
      redrawCanvas();
    }
  }
  
  function updateUndoButton() {
    undoBtn.disabled = annotationHistory.length === 0;
  }
  
  undoBtn.addEventListener('click', undo);
  
  // Keyboard shortcuts
  document.addEventListener('keydown', (e) => {
    if (e.ctrlKey && e.key === 'z' && !e.shiftKey) {
      e.preventDefault();
      undo();
    }
  });
  
  // Save initial state
  saveHistory();

  // Zoom controls
  const zoomInBtn = document.getElementById('zoom-in');
  const zoomOutBtn = document.getElementById('zoom-out');
  const resetBtn = document.getElementById('reset-view');

  zoomInBtn.addEventListener('click', () => {
    const newScale = Math.min(MAX_SCALE, scale * 1.2);
    if (newScale !== scale) {
      const rect = canvas.getBoundingClientRect();
      const centerX = rect.width / 2;
      const centerY = rect.height / 2;
      const scaleFactor = newScale / scale;
      panOffset.x = centerX - (centerX - panOffset.x) * scaleFactor;
      panOffset.y = centerY - (centerY - panOffset.y) * scaleFactor;
      scale = newScale;
      redrawCanvas();
    }
  });

  zoomOutBtn.addEventListener('click', () => {
    const newScale = Math.max(MIN_SCALE, scale * 0.8);
    if (newScale !== scale) {
      const rect = canvas.getBoundingClientRect();
      const centerX = rect.width / 2;
      const centerY = rect.height / 2;
      const scaleFactor = newScale / scale;
      panOffset.x = centerX - (centerX - panOffset.x) * scaleFactor;
      panOffset.y = centerY - (centerY - panOffset.y) * scaleFactor;
      scale = newScale;
      redrawCanvas();
    }
  });

  resetBtn.addEventListener('click', () => {
    scale = 1;
    panOffset = { x: 0, y: 0 };
    redrawCanvas();
  });





  // Mouse wheel zoom
  canvas.addEventListener('wheel', (e) => {
    e.preventDefault();
    const rect = canvas.getBoundingClientRect();
    const mouseX = e.clientX - rect.left;
    const mouseY = e.clientY - rect.top;
    
    const delta = e.deltaY > 0 ? 0.9 : 1.1;
    const newScale = Math.max(MIN_SCALE, Math.min(MAX_SCALE, scale * delta));
    
    if (newScale !== scale) {
      const scaleFactor = newScale / scale;
      panOffset.x = mouseX - (mouseX - panOffset.x) * scaleFactor;
      panOffset.y = mouseY - (mouseY - panOffset.y) * scaleFactor;
      scale = newScale;
      redrawCanvas();
    }
  });

  // Pan with space + drag
  
  document.addEventListener('keydown', (e) => {
    if (e.code === 'Space') {
      spacePressed = true;
      canvas.style.cursor = 'grab';
    }
  });
  
  document.addEventListener('keyup', (e) => {
    if (e.code === 'Space') {
      spacePressed = false;
      canvas.style.cursor = 'default';
    }
  });

  saveBtn.addEventListener('click', () => {
    selection = null; // Hide selection box before saving
    redrawCanvas();
    const dataUrl = canvas.toDataURL('image/png');
    chrome.storage.local.get({ screenshots: [] }, (result) => {
      const screenshots = result.screenshots;
      screenshots.push({ id: Date.now(), data: dataUrl });
      chrome.storage.local.set({ screenshots: screenshots }, () => {
        alert('截图已保存！');
        window.close();
      });
    });
  });

  cancelBtn.addEventListener('click', () => {
    if (confirm('确定要放弃此次编辑吗？')) {
      window.close();
    }
  });
});