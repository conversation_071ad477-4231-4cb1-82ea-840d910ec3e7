<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>截图小工具</title>
  <style>
    body {
      width: 200px;
      text-align: center;
      font-family: sans-serif;
      padding: 10px;
      background: #f8f9fa;
    }
    button {
      width: 150px;
      margin: 5px;
      padding: 10px 16px;
      cursor: pointer;
      background: #007bff;
      color: white;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.2s ease;
      height: 40px;
      box-sizing: border-box;
    }
    button:hover {
      background: #0056b3;
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
    }
    button:active {
      transform: translateY(0);
      box-shadow: 0 1px 4px rgba(0, 123, 255, 0.3);
    }
    a {
      display: block;
      margin-top: 10px;
      padding: 10px 16px;
      background: #ff9800;
      color: white;
      text-decoration: none;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.2s ease;
      height: 40px;
      box-sizing: border-box;
      line-height: 20px;
    }
    a:hover {
      background: #f57c00;
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(255, 152, 0, 0.3);
    }
    a:active {
      transform: translateY(0);
    }
  </style>
</head>
<body>
  <button id="capture-full">全屏截图</button>
  <button id="capture-region">区域截图</button>
  <a href="gallery.html" target="_blank">查看相册</a>
  <script src="popup.js"></script>
</body>
</html>