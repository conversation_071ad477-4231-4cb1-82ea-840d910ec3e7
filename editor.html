<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>截图编辑器</title>
  <style>
    body, html { margin: 0; padding: 0; background-color: #f0f0f0; display: flex; flex-direction: column; align-items: center; font-family: Arial, sans-serif; }
    #toolbar {
      padding: 10px;
      background-color: #fff;
      border-bottom: 1px solid #ccc;
      width: 100%;
      text-align: center;
      box-sizing: border-box;
      display: flex;
      gap: 10px;
      justify-content: center;
      align-items: center;
      flex-wrap: wrap;
    }
    #toolbar button, #toolbar input[type='color'], #toolbar input[type='range'] {
      margin: 0 5px;
      padding: 10px 16px;
      border: none;
      background: #007bff;
      color: white;
      cursor: pointer;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.2s ease;
      min-width: 80px;
      height: 40px;
      box-sizing: border-box;
    }
    #toolbar button:hover {
      background: #0056b3;
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
    }
    #toolbar button:active, #toolbar button.active {
      background: #004085;
      color: white;
      transform: translateY(0);
    }
    #toolbar button:disabled {
      background: #6c757d;
      color: #fff;
      cursor: not-allowed;
      opacity: 0.6;
      transform: none;
      box-shadow: none;
    }
    #toolbar input[type='color'] {
      background: white;
      border: 2px solid #007bff;
    }
    #toolbar input[type='range'] {
      background: #007bff;
    }
    #canvas-container {
      margin-top: 20px;
      box-shadow: 0 0 10px rgba(0,0,0,0.2);
      display: flex;
      justify-content: center;
      overflow: auto;
    }
    canvas { 
      display: block; 
      background: white; 
      cursor: crosshair; 
      max-width: 100%;
      max-height: 80vh;
    }
    input[type="color"] {
      width: 40px;
      height: 30px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    input[type="range"] {
      width: 100px;
    }
  </style>
</head>
<body>

  <div id="toolbar">
    <button id="tool-arrow">箭头</button>
    <button id="tool-text">文本</button>
    <button id="tool-rect">框选</button>
    <input type="color" id="color-picker" value="#ff0000">
    <input type="range" id="line-width" min="1" max="20" value="5">
    <button id="zoom-out">缩小</button>
    <button id="zoom-in">放大</button>
    <button id="reset-view">重置</button>
    <button id="undo-btn">撤销</button>
    <button id="save-btn">保存</button>
    <button id="cancel-btn">取消</button>
  </div>

  <div id="canvas-container">
    <canvas id="editor-canvas"></canvas>
  </div>

  <script src="editor.js"></script>

</body>
</html>