<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>截图编辑器</title>
  <style>
    body, html { margin: 0; padding: 0; background-color: #f0f0f0; display: flex; flex-direction: column; align-items: center; font-family: Arial, sans-serif; }
    #toolbar {
      padding: 10px;
      background-color: #fff;
      border-bottom: 1px solid #ccc;
      width: 100%;
      text-align: center;
      box-sizing: border-box;
      display: flex;
      gap: 10px;
      justify-content: center;
      align-items: center;
      flex-wrap: wrap;
    }
    #toolbar button, #toolbar input[type='color'], #toolbar input[type='range'] {
      margin: 0 5px;
      padding: 8px 12px;
      border: 1px solid #ccc;
      background: white;
      cursor: pointer;
      border-radius: 4px;
      font-size: 14px;
    }
    #toolbar button:hover {
        background: #f5f5f5;
      }
      #toolbar button:active, #toolbar button.active {
        background: #007bff;
        color: white;
      }
      #toolbar button:disabled {
        background: #e0e0e0;
        color: #999;
        cursor: not-allowed;
      }
    #canvas-container {
      margin-top: 20px;
      box-shadow: 0 0 10px rgba(0,0,0,0.2);
      display: flex;
      justify-content: center;
      overflow: auto;
    }
    canvas { 
      display: block; 
      background: white; 
      cursor: crosshair; 
      max-width: 100%;
      max-height: 80vh;
    }
    input[type="color"] {
      width: 40px;
      height: 30px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    input[type="range"] {
      width: 100px;
    }
  </style>
</head>
<body>

  <div id="toolbar">
    <button id="tool-arrow">箭头</button>
    <button id="tool-text">文本</button>
    <button id="tool-rect">框选</button>
    <input type="color" id="color-picker" value="#ff0000">
    <input type="range" id="line-width" min="1" max="20" value="5">
    <button id="zoom-out">缩小</button>
    <button id="zoom-in">放大</button>
    <button id="reset-view">重置</button>
    <button id="undo-btn">撤销</button>
    <button id="save-btn">保存</button>
    <button id="cancel-btn">取消</button>
  </div>

  <div id="canvas-container">
    <canvas id="editor-canvas"></canvas>
  </div>

  <script src="editor.js"></script>

</body>
</html>