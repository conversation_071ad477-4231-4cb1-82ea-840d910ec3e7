<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图库布局预览</title>
    <style>
        body { 
            font-family: sans-serif; 
            background: #f8f9fa;
            margin: 0;
            padding: 20px;
        }
        h1 { 
            text-align: center; 
            color: #333;
            margin-bottom: 30px;
        }
        #gallery { 
            display: flex; 
            flex-wrap: wrap; 
            gap: 20px; 
            justify-content: center; 
        }
        .screenshot-item { 
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px; 
            text-align: center;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
            position: relative;
            padding-bottom: 50px;
        }
        .screenshot-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .screenshot-item img { 
            max-width: 300px; 
            max-height: 200px; 
            display: block; 
            margin-bottom: 15px;
            border-radius: 4px;
        }
        .screenshot-item .actions {
            position: absolute;
            bottom: 10px;
            right: 10px;
            display: flex;
            gap: 8px;
        }
        .screenshot-item .actions button {
            margin: 0;
            padding: 6px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
            width: 32px;
            height: 32px;
            box-sizing: border-box;
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .screenshot-item .actions button:hover {
            background: #0056b3;
            transform: translateY(-1px);
            box-shadow: 0 2px 6px rgba(0,0,0,0.3);
        }
        .screenshot-item .actions button.delete {
            background: #dc3545;
        }
        .screenshot-item .actions button.delete:hover {
            background: #c82333;
            transform: translateY(-1px);
            box-shadow: 0 2px 6px rgba(220,53,69,0.3);
        }
    </style>
</head>
<body>
    <h1>📸 图库布局预览</h1>
    
    <div id="gallery">
        <!-- 示例截图卡片 1 -->
        <div class="screenshot-item">
            <img src="https://via.placeholder.com/300x200/e3f2fd/333?text=截图示例1" alt="截图1">
            <div class="actions">
                <button title="下载图片">📥</button>
                <button class="delete" title="删除图片">🗑️</button>
            </div>
        </div>
        
        <!-- 示例截图卡片 2 -->
        <div class="screenshot-item">
            <img src="https://via.placeholder.com/300x200/fff2cc/333?text=截图示例2" alt="截图2">
            <div class="actions">
                <button title="下载图片">📥</button>
                <button class="delete" title="删除图片">🗑️</button>
            </div>
        </div>
        
        <!-- 示例截图卡片 3 -->
        <div class="screenshot-item">
            <img src="https://via.placeholder.com/300x200/f3e5f5/333?text=截图示例3" alt="截图3">
            <div class="actions">
                <button title="下载图片">📥</button>
                <button class="delete" title="删除图片">🗑️</button>
            </div>
        </div>
        
        <!-- 示例截图卡片 4 -->
        <div class="screenshot-item">
            <img src="https://via.placeholder.com/300x200/e8f5e8/333?text=截图示例4" alt="截图4">
            <div class="actions">
                <button title="下载图片">📥</button>
                <button class="delete" title="删除图片">🗑️</button>
            </div>
        </div>
    </div>
    
    <script>
        // 添加交互效果演示
        document.querySelectorAll('.screenshot-item .actions button').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                if (btn.classList.contains('delete')) {
                    if (confirm('确定要删除这张截图吗？')) {
                        btn.closest('.screenshot-item').style.opacity = '0.5';
                        setTimeout(() => {
                            btn.closest('.screenshot-item').remove();
                        }, 300);
                    }
                } else {
                    // 下载按钮效果
                    btn.style.background = '#28a745';
                    btn.innerHTML = '✓';
                    setTimeout(() => {
                        btn.style.background = '#007bff';
                        btn.innerHTML = '📥';
                    }, 1000);
                }
            });
        });
    </script>
</body>
</html>
