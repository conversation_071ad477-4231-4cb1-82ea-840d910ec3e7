# Chrome 截图插件

一个功能强大的Chrome浏览器截图扩展，支持全屏截图、区域截图和图片编辑功能。

## 功能特性

### 📸 截图功能
- **全屏截图**：一键截取当前页面的完整截图
- **区域截图**：自由选择页面区域进行截图
- **批量管理**：支持批量下载和管理截图

### 🎨 编辑功能
- **图形标注**：矩形框、箭头等标注工具
- **文字添加**：在截图上添加文字说明
- **颜色选择**：多种颜色和画笔大小
- **撤销重做**：支持操作历史记录

### 💾 存储管理
- **本地存储**：截图保存在浏览器本地存储
- **自动清理**：智能管理存储空间，自动清理旧截图
- **备份机制**：自动备份最近100张截图

## 最新优化 (v1.1)

### 🔧 修复的问题
1. **权限配置**：修复了manifest.json中缺少tabs权限的问题
2. **重复监听器**：合并了background.js中重复的消息监听器
3. **防重复注入**：加强了content script的防重复注入机制
4. **错误处理**：完善了全局错误处理和用户反馈

### 🚀 性能优化
1. **存储管理**：
   - 添加存储空间检查
   - 自动清理机制（保留最新50张截图）
   - 智能备份系统

2. **用户体验**：
   - 按钮加载状态指示器
   - 防重复点击保护
   - 优雅的通知系统
   - 自动关闭popup窗口

3. **界面改进**：
   - 新增加载动画
   - 改进按钮状态反馈
   - 优化通知显示效果

## 安装使用

### 开发模式安装
1. 打开Chrome浏览器，进入 `chrome://extensions/`
2. 开启"开发者模式"
3. 点击"加载已解压的扩展程序"
4. 选择项目文件夹

### 使用方法
1. **全屏截图**：点击插件图标 → 选择"全屏截图"
2. **区域截图**：点击插件图标 → 选择"区域截图" → 在页面上拖拽选择区域
3. **查看图库**：点击插件图标 → 选择"查看图库"
4. **编辑截图**：在区域截图时选择"编辑后保存"，或在图库中点击"编辑"

## 技术架构

### 文件结构
```
image-modul/
├── manifest.json          # 扩展配置文件
├── background.js          # 后台脚本
├── content.js            # 内容脚本
├── popup.html/js         # 弹出窗口
├── gallery.html/js       # 图库页面
├── editor.html/js        # 编辑器页面
└── package.json          # 依赖配置
```

### 核心技术
- **Chrome Extension API**：扩展核心功能
- **Canvas API**：图片处理和编辑
- **Chrome Storage API**：本地数据存储
- **HTML5 File API**：文件下载功能

## 开发说明

### 环境要求
- Chrome 88+
- Node.js (可选，用于依赖管理)

### 依赖包
```json
{
  "dependencies": {
    "jszip": "^3.10.1"
  }
}
```

### 调试技巧
1. **后台脚本调试**：在扩展管理页面点击"检查视图"
2. **内容脚本调试**：在页面上按F12，查看Console
3. **存储调试**：使用Chrome DevTools的Application面板

## 常见问题

### Q: 截图失败怎么办？
A: 请检查：
- 是否在特殊页面（chrome://、扩展页面等）
- 是否刷新页面后重试
- 查看控制台错误信息

### Q: 存储空间不足？
A: 插件会自动清理旧截图，也可以手动清空图库

### Q: 编辑器打不开？
A: 请确保浏览器允许弹出窗口，或检查是否被广告拦截器阻止

## 更新日志

### v1.1 (2024-01-XX)
- 🔧 修复权限配置问题
- 🚀 优化存储管理机制
- 💫 改进用户界面体验
- 🛡️ 加强错误处理
- 📱 优化响应式设计

### v1.0 (2024-01-XX)
- 🎉 初始版本发布
- 📸 基础截图功能
- 🎨 图片编辑功能
- 💾 本地存储功能

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目！

## 许可证

MIT License