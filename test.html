<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>截图插件测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #007bff;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .test-section h2 {
            color: #007bff;
            margin-top: 0;
        }
        
        .highlight-box {
            background: #ffeb3b;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border: 2px dashed #ff9800;
        }
        
        .image-placeholder {
            width: 200px;
            height: 150px;
            background: #e0e0e0;
            border: 2px solid #999;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 10px;
            border-radius: 5px;
        }
        
        .flex-container {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }
        
        .instructions {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #2196f3;
            margin: 20px 0;
        }
        
        .instructions h3 {
            margin-top: 0;
            color: #1976d2;
        }
        
        .instructions ol {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .instructions li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ Chrome截图插件测试页面</h1>
        
        <div class="instructions">
            <h3>📋 测试说明</h3>
            <ol>
                <li>点击浏览器工具栏中的截图插件图标</li>
                <li>选择"区域截图"</li>
                <li>拖拽选择下面任意一个测试区域</li>
                <li>检查是否能正确进入编辑器</li>
                <li>验证截图是否高清且无多余边框</li>
            </ol>
            <div style="margin-top: 15px;">
                <button id="testBtn" style="background: #4caf50; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
                    🧪 测试Content Script注入
                </button>
                <span id="testResult" style="margin-left: 10px; font-weight: bold;"></span>
            </div>
        </div>
        
        <div class="test-section">
            <h2>🎯 测试区域 1 - 文本内容</h2>
            <p>这是一个包含文本的测试区域。请尝试选择这个区域进行截图。</p>
            <div class="highlight-box">
                <strong>重点内容：</strong>这里是一些重要的文字，用于测试截图的文字清晰度。
                包含中文、English、数字123和特殊符号！@#$%^&*()
            </div>
        </div>
        
        <div class="test-section">
            <h2>🖼️ 测试区域 2 - 图像元素</h2>
            <div class="flex-container">
                <div class="image-placeholder">图像占位符 1</div>
                <div class="image-placeholder">图像占位符 2</div>
                <div class="image-placeholder">图像占位符 3</div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>🌈 测试区域 3 - 彩色内容</h2>
            <div style="display: flex; gap: 10px; margin: 10px 0;">
                <div style="width: 50px; height: 50px; background: #f44336; border-radius: 50%;"></div>
                <div style="width: 50px; height: 50px; background: #4caf50; border-radius: 50%;"></div>
                <div style="width: 50px; height: 50px; background: #2196f3; border-radius: 50%;"></div>
                <div style="width: 50px; height: 50px; background: #ff9800; border-radius: 50%;"></div>
                <div style="width: 50px; height: 50px; background: #9c27b0; border-radius: 50%;"></div>
            </div>
            <p>这个区域包含多种颜色，用于测试截图的色彩还原度。</p>
        </div>
        
        <div class="test-section">
            <h2>📏 测试区域 4 - 精确边界</h2>
            <div style="border: 3px solid #e91e63; padding: 20px; background: #fce4ec;">
                <p>这个区域有明显的边界，用于测试截图的精确性。</p>
                <p>请确保截图时边界清晰，没有多余的空白或被截断。</p>
            </div>
        </div>
    </div>
    
    <script>
        // 添加一些调试信息
        console.log('测试页面已加载');
        console.log('页面尺寸:', window.innerWidth, 'x', window.innerHeight);
        console.log('设备像素比:', window.devicePixelRatio);

        // 监听页面滚动
        window.addEventListener('scroll', () => {
            console.log('页面滚动位置:', window.scrollX, window.scrollY);
        });

        // 测试按钮功能
        document.addEventListener('DOMContentLoaded', () => {
            const testBtn = document.getElementById('testBtn');
            const testResult = document.getElementById('testResult');

            testBtn.addEventListener('click', () => {
                // 检查是否有截图overlay注入的标志
                if (window.screenshotOverlayInjected) {
                    testResult.textContent = '✅ Content Script已注入';
                    testResult.style.color = 'green';
                } else {
                    testResult.textContent = '❌ Content Script未注入';
                    testResult.style.color = 'red';
                }

                // 检查是否有overlay元素
                const overlays = document.querySelectorAll('[data-screenshot-overlay]');
                console.log('找到的overlay元素:', overlays.length);

                if (overlays.length > 0) {
                    testResult.textContent += ' (发现overlay元素)';
                }
            });
        });
    </script>
</body>
</html>
