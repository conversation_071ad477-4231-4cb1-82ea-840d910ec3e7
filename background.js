// background.js

let tempScreenshotData = null;

chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  switch (request.action) {
    case "captureVisibleTab":
      // 标准截图，用于全屏截图
      chrome.tabs.captureVisibleTab({ format: "png" }, (dataUrl) => {
        sendResponse({ dataUrl: dataUrl });
      });
      return true; // Async response

    case "captureHighQualityTab":
      // 高质量截图，用于区域截图
      chrome.tabs.captureVisibleTab({
        format: "png",
        quality: 100  // 最高质量
      }, (dataUrl) => {
        if (!dataUrl) {
          sendResponse({ error: "Failed to capture screenshot" });
          return;
        }

        // 如果有裁剪区域，进行精确裁剪
        if (request.cropArea) {
          cropImageData(dataUrl, request.cropArea, (croppedDataUrl) => {
            sendResponse({ dataUrl: croppedDataUrl });
          });
        } else {
          sendResponse({ dataUrl: dataUrl });
        }
      });
      return true; // Async response

    case "openEditor":
      tempScreenshotData = request.dataUrl;
      // 保存原始裁剪区域信息，用于编辑器参考
      if (request.originalCropArea) {
        tempScreenshotData = {
          dataUrl: request.dataUrl,
          cropArea: request.originalCropArea
        };
      }
      chrome.tabs.create({ url: chrome.runtime.getURL("editor.html") });
      sendResponse({ success: true });
      return true;

    case "getScreenshotData":
      if (tempScreenshotData) {
        // 兼容旧格式和新格式
        const response = typeof tempScreenshotData === 'string'
          ? { dataUrl: tempScreenshotData }
          : tempScreenshotData;
        sendResponse(response);
        // 延迟清空数据，给editor足够时间加载
        setTimeout(() => {
          tempScreenshotData = null;
        }, 2000);
      } else {
        sendResponse({}); // Send empty response if no data
      }
      return true;

    default:
      // Handle other cases or do nothing
      break;
  }
});

// 高精度图片裁剪函数
function cropImageData(dataUrl, cropArea, callback) {
  const img = new Image();
  img.onload = () => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    // 设置画布尺寸为裁剪区域尺寸
    canvas.width = cropArea.width;
    canvas.height = cropArea.height;

    // 启用图像平滑以获得更好的质量
    ctx.imageSmoothingEnabled = true;
    ctx.imageSmoothingQuality = 'high';

    // 精确裁剪图像
    ctx.drawImage(
      img,
      cropArea.left,
      cropArea.top,
      cropArea.width,
      cropArea.height,
      0,
      0,
      cropArea.width,
      cropArea.height
    );

    // 输出高质量PNG
    const croppedDataUrl = canvas.toDataURL('image/png', 1.0);
    callback(croppedDataUrl);
  };
  img.onerror = () => {
    console.error('Failed to load image for cropping');
    callback(dataUrl); // 返回原图作为备选
  };
  img.src = dataUrl;
}