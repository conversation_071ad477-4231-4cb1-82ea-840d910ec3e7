// background.js

let tempScreenshotData = null;

chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  switch (request.action) {
    case "captureVisibleTab":
      chrome.tabs.captureVisibleTab({ format: "png" }, (dataUrl) => {
        sendResponse({ dataUrl: dataUrl });
      });
      return true; // Async response

    case "openEditor":
      tempScreenshotData = request.dataUrl;
      chrome.tabs.create({ url: chrome.runtime.getURL("editor.html") });
      sendResponse({ success: true });
      return true;

    case "getScreenshotData":
      if (tempScreenshotData) {
        sendResponse({ dataUrl: tempScreenshotData });
        // 延迟清空数据，给editor足够时间加载
        setTimeout(() => {
          tempScreenshotData = null;
        }, 2000);
      } else {
        sendResponse({}); // Send empty response if no data
      }
      return true;

    default:
      // Handle other cases or do nothing
      break;
  }
});