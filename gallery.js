// gallery.js
document.addEventListener('DOMContentLoaded', () => {
  const gallery = document.getElementById('gallery');
  const downloadAllBtn = document.getElementById('download-all');
  const clearAllBtn = document.getElementById('clear-all');

  function renderScreenshots() {
    gallery.innerHTML = '';
    chrome.storage.local.get({ screenshots: [] }, (result) => {
      const screenshots = result.screenshots;
      if (screenshots.length === 0) {
        gallery.innerHTML = '<p>还没有截图，快去截一张吧！</p>';
        downloadAllBtn.disabled = true;
        clearAllBtn.disabled = true;
        return;
      }

      downloadAllBtn.disabled = false;
      clearAllBtn.disabled = false;

      screenshots.forEach((screenshot, index) => {
        const item = document.createElement('div');
        item.className = 'screenshot-item';

        const img = document.createElement('img');
        img.src = screenshot.data;

        const actions = document.createElement('div');
        actions.className = 'actions';

        const downloadBtn = document.createElement('button');
        downloadBtn.textContent = '下载';
        downloadBtn.addEventListener('click', () => {
          const a = document.createElement('a');
          a.href = screenshot.data;
          a.download = `screenshot-${screenshot.id}.png`;
          a.click();
        });

        const deleteBtn = document.createElement('button');
        deleteBtn.textContent = '删除';
        deleteBtn.addEventListener('click', () => {
          screenshots.splice(index, 1);
          chrome.storage.local.set({ screenshots: screenshots }, renderScreenshots);
        });

        actions.appendChild(downloadBtn);
        actions.appendChild(deleteBtn);
        item.appendChild(img);
        item.appendChild(actions);
        gallery.appendChild(item);
      });
    });
  }

  downloadAllBtn.addEventListener('click', () => {
    chrome.storage.local.get({ screenshots: [] }, (result) => {
      const screenshots = result.screenshots;
      if (screenshots.length === 0) return;

      const zip = new JSZip();
      screenshots.forEach((shot) => {
        const base64Data = shot.data.split(',')[1];
        zip.file(`screenshot-${shot.id}.png`, base64Data, { base64: true });
      });

      zip.generateAsync({ type: 'blob' }).then((content) => {
        const a = document.createElement('a');
        a.href = URL.createObjectURL(content);
        a.download = 'screenshots.zip';
        a.click();
        URL.revokeObjectURL(a.href);
      });
    });
  });

  clearAllBtn.addEventListener('click', () => {
    if (confirm('确定要清空所有截图吗？')) {
      chrome.storage.local.set({ screenshots: [] }, renderScreenshots);
    }
  });

  renderScreenshots();
});