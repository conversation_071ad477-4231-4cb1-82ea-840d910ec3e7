// content.js - 区域截图功能

console.log('🚀 Content script starting...');
console.log('📍 Current URL:', window.location.href);

// 防重复注入检查
if (window.screenshotOverlayInjected) {
  console.log('⚠️ Already injected, skipping...');
} else {
  // 检查是否在特殊页面
  if (window.location.protocol === 'chrome:' ||
      window.location.protocol === 'chrome-extension:') {
    console.error('❌ Cannot inject on special pages');
    alert('无法在此类页面使用截图功能');
  } else {
    initScreenshot();
  }
}

function initScreenshot() {
  console.log('🎯 Initializing screenshot overlay...');
  window.screenshotOverlayInjected = true;

  // 清理旧overlay
  const existingOverlays = document.querySelectorAll('[data-screenshot-overlay]');
  existingOverlays.forEach(overlay => overlay.remove());

  // 确保DOM已加载
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', createOverlay);
  } else {
    createOverlay();
  }
}

function createOverlay() {
  console.log('🎯 Creating overlay...');

  if (!document.body) {
    setTimeout(createOverlay, 100);
    return;
  }

  // 创建遮罩层
  const overlay = document.createElement('div');
  overlay.setAttribute('data-screenshot-overlay', 'true');
  overlay.style.cssText = `
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    background: rgba(0, 0, 0, 0.3) !important;
    cursor: crosshair !important;
    z-index: 2147483647 !important;
    pointer-events: auto !important;
  `;

  document.body.appendChild(overlay);
  console.log('✅ Overlay created');

  let startX, startY, selectionBox;

  function cleanupOverlay() {
    if (selectionBox && document.body.contains(selectionBox)) {
      document.body.removeChild(selectionBox);
    }
    if (overlay && document.body.contains(overlay)) {
      document.body.removeChild(overlay);
    }
    window.screenshotOverlayInjected = false;
    selectionBox = null;
  }

  overlay.addEventListener('mousedown', (e) => {
    e.preventDefault();
    startX = e.clientX;
    startY = e.clientY;
    console.log('🖱️ Selection started:', startX, startY);

    selectionBox = document.createElement('div');
    selectionBox.style.cssText = `
      position: fixed !important;
      border: 2px solid #007bff !important;
      background: rgba(0, 123, 255, 0.1) !important;
      pointer-events: none !important;
      z-index: 2147483648 !important;
      left: ${startX}px !important;
      top: ${startY}px !important;
    `;

    document.body.appendChild(selectionBox);
  });

  overlay.addEventListener('mousemove', (e) => {
    if (!selectionBox) return;

    const width = e.clientX - startX;
    const height = e.clientY - startY;
    selectionBox.style.width = Math.abs(width) + 'px';
    selectionBox.style.height = Math.abs(height) + 'px';
    selectionBox.style.left = (width > 0 ? startX : e.clientX) + 'px';
    selectionBox.style.top = (height > 0 ? startY : e.clientY) + 'px';
  });

  overlay.addEventListener('mouseup', (e) => {
    if (!selectionBox) return;

    const rect = selectionBox.getBoundingClientRect();
    console.log('📏 Selection:', rect);

    if (rect.width < 10 || rect.height < 10) {
      cleanupOverlay();
      alert('选择区域太小');
      return;
    }

    const dpr = window.devicePixelRatio || 1;
    const cropArea = {
      left: Math.round(rect.left * dpr),
      top: Math.round(rect.top * dpr),
      width: Math.round(rect.width * dpr),
      height: Math.round(rect.height * dpr)
    };

    cleanupOverlay();
    captureAndCrop(cropArea);
  });

  document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
      cleanupOverlay();
    }
  });
}

function captureAndCrop(cropArea) {
  console.log('📸 Capturing...');

  chrome.runtime.sendMessage({ action: "captureVisibleTab" }, (response) => {
    if (!response || !response.dataUrl) {
      alert('截图失败');
      return;
    }

    const img = new Image();
    img.onload = () => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      canvas.width = cropArea.width;
      canvas.height = cropArea.height;

      ctx.drawImage(
        img,
        cropArea.left, cropArea.top, cropArea.width, cropArea.height,
        0, 0, cropArea.width, cropArea.height
      );

      const croppedDataUrl = canvas.toDataURL('image/png');

      chrome.runtime.sendMessage({
        action: "openEditor",
        dataUrl: croppedDataUrl
      });
    };

    img.src = response.dataUrl;
  });
}