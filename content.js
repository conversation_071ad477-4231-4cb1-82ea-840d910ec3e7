// content.js

// 防重复注入检查
if (window.screenshotOverlayInjected) {
  console.log('Screenshot overlay already injected, skipping...');
  return;
}
window.screenshotOverlayInjected = true;

// 清理可能存在的旧overlay
const existingOverlays = document.querySelectorAll('[data-screenshot-overlay]');
existingOverlays.forEach(overlay => overlay.remove());

const overlay = document.createElement('div');
overlay.setAttribute('data-screenshot-overlay', 'true');
overlay.style.position = 'fixed';
overlay.style.top = '0';
overlay.style.left = '0';
overlay.style.width = '100%';
overlay.style.height = '100%';
overlay.style.background = 'rgba(0, 0, 0, 0.5)';
overlay.style.cursor = 'crosshair';
overlay.style.zIndex = '999999';
document.body.appendChild(overlay);

let startX, startY, selectionBox;

// 清理函数
function cleanupOverlay() {
  if (selectionBox && document.body.contains(selectionBox)) {
    document.body.removeChild(selectionBox);
  }
  if (overlay && document.body.contains(overlay)) {
    document.body.removeChild(overlay);
  }
  window.screenshotOverlayInjected = false;
  selectionBox = null;
}

overlay.addEventListener('mousedown', (e) => {
  startX = e.clientX;
  startY = e.clientY;
  selectionBox = document.createElement('div');
  selectionBox.style.position = 'fixed';
  selectionBox.style.border = '2px dashed #fff';
  selectionBox.style.zIndex = '1000000';
  selectionBox.style.left = startX + 'px';
  selectionBox.style.top = startY + 'px';
  document.body.appendChild(selectionBox);
});

overlay.addEventListener('mousemove', (e) => {
  if (!selectionBox) return;
  const width = e.clientX - startX;
  const height = e.clientY - startY;
  selectionBox.style.width = Math.abs(width) + 'px';
  selectionBox.style.height = Math.abs(height) + 'px';
  selectionBox.style.left = (width > 0 ? startX : e.clientX) + 'px';
  selectionBox.style.top = (height > 0 ? startY : e.clientY) + 'px';
});

overlay.addEventListener('mouseup', () => {
  if (selectionBox) {
    const rect = selectionBox.getBoundingClientRect();

    // 确保选择区域有效（最小10x10像素）
    if (rect.width < 10 || rect.height < 10) {
      cleanupOverlay();
      alert('选择区域太小，请重新选择');
      return;
    }

    // 获取精确的选择区域坐标（去除边框影响）
    const dpr = window.devicePixelRatio || 1;
    const scrollX = window.scrollX || window.pageXOffset;
    const scrollY = window.scrollY || window.pageYOffset;

    // 计算精确的裁剪区域，考虑设备像素比和滚动偏移
    const cropArea = {
      left: Math.round((rect.left + scrollX) * dpr),
      top: Math.round((rect.top + scrollY) * dpr),
      width: Math.round(rect.width * dpr),
      height: Math.round(rect.height * dpr)
    };

    console.log('Crop area:', cropArea, 'DPR:', dpr);

    // 清理选择框和遮罩
    document.body.removeChild(selectionBox);
    document.body.removeChild(overlay);
    window.screenshotOverlayInjected = false;

    // 使用高质量截图API
    chrome.runtime.sendMessage({
      action: "captureHighQualityTab",
      cropArea: cropArea
    }, (response) => {
      if (!response || !response.dataUrl) {
        alert('截图失败，请重试');
        cleanupOverlay();
        return;
      }

      console.log('High quality screenshot captured, opening editor...');
      chrome.runtime.sendMessage({
        action: "openEditor",
        dataUrl: response.dataUrl,
        originalCropArea: cropArea
      }, (response) => {
        console.log('openEditor response:', response);
        if (!response || !response.success) {
          alert('打开编辑器失败，请重试');
        }
      });
    });
  } else {
    cleanupOverlay();
  }
});