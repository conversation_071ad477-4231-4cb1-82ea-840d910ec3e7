// content.js

const overlay = document.createElement('div');
overlay.style.position = 'fixed';
overlay.style.top = '0';
overlay.style.left = '0';
overlay.style.width = '100%';
overlay.style.height = '100%';
overlay.style.background = 'rgba(0, 0, 0, 0.5)';
overlay.style.cursor = 'crosshair';
overlay.style.zIndex = '999999';
document.body.appendChild(overlay);

let startX, startY, selectionBox;

overlay.addEventListener('mousedown', (e) => {
  startX = e.clientX;
  startY = e.clientY;
  selectionBox = document.createElement('div');
  selectionBox.style.position = 'fixed';
  selectionBox.style.border = '2px dashed #fff';
  selectionBox.style.zIndex = '1000000';
  selectionBox.style.left = startX + 'px';
  selectionBox.style.top = startY + 'px';
  document.body.appendChild(selectionBox);
});

overlay.addEventListener('mousemove', (e) => {
  if (!selectionBox) return;
  const width = e.clientX - startX;
  const height = e.clientY - startY;
  selectionBox.style.width = Math.abs(width) + 'px';
  selectionBox.style.height = Math.abs(height) + 'px';
  selectionBox.style.left = (width > 0 ? startX : e.clientX) + 'px';
  selectionBox.style.top = (height > 0 ? startY : e.clientY) + 'px';
});

overlay.addEventListener('mouseup', (e) => {
  if (selectionBox) {
    const rect = selectionBox.getBoundingClientRect();
    
    // 确保选择区域有效
    if (rect.width < 5 || rect.height < 5) {
      document.body.removeChild(overlay);
      document.body.removeChild(selectionBox);
      return;
    }

    // 清理选择框和遮罩
    document.body.removeChild(selectionBox);
    document.body.removeChild(overlay);

    // 计算包含滚动偏移的绝对坐标
    const scrollX = window.scrollX || window.pageXOffset;
    const scrollY = window.scrollY || window.pageYOffset;
    const cropArea = {
      left: rect.left + scrollX,
      top: rect.top + scrollY,
      width: rect.width,
      height: rect.height
    };

    chrome.runtime.sendMessage({ action: "captureVisibleTab" }, (response) => {
      if (!response || !response.dataUrl) {
        alert('截图失败，请重试');
        return;
      }
      
      const img = new Image();
      img.onload = () => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const dpr = window.devicePixelRatio || 1;
        
        // 设置画布尺寸为选择区域尺寸
        canvas.width = cropArea.width * dpr;
        canvas.height = cropArea.height * dpr;
        
        // 绘制选择区域
        ctx.drawImage(
          img,
          cropArea.left * dpr,
          cropArea.top * dpr,
          cropArea.width * dpr,
          cropArea.height * dpr,
          0,
          0,
          canvas.width,
          canvas.height
        );
        
        const croppedDataUrl = canvas.toDataURL('image/png');
        console.log('Opening editor with cropped image');
        chrome.runtime.sendMessage({ action: "openEditor", dataUrl: croppedDataUrl }, (response) => {
          console.log('openEditor response:', response);
          if (!response || !response.success) {
            alert('打开编辑器失败，请重试');
          }
        });
      };
      img.onerror = () => {
        alert('截图失败，请重试');
      };
      img.src = response.dataUrl;
    });
  } else {
    document.body.removeChild(overlay);
  }
});