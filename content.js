// content.js

// 防重复注入检查
if (window.screenshotOverlayInjected) {
  console.log('Screenshot overlay already injected, skipping...');
  return;
}
window.screenshotOverlayInjected = true;

console.log('Content script injected successfully');
console.log('Current URL:', window.location.href);
console.log('Document ready state:', document.readyState);

// 清理可能存在的旧overlay
const existingOverlays = document.querySelectorAll('[data-screenshot-overlay]');
existingOverlays.forEach(overlay => overlay.remove());

const overlay = document.createElement('div');
overlay.setAttribute('data-screenshot-overlay', 'true');
overlay.style.position = 'fixed';
overlay.style.top = '0';
overlay.style.left = '0';
overlay.style.width = '100%';
overlay.style.height = '100%';
overlay.style.background = 'rgba(0, 0, 0, 0.5)';
overlay.style.cursor = 'crosshair';
overlay.style.zIndex = '999999';
document.body.appendChild(overlay);

let startX, startY, selectionBox;

// 清理函数
function cleanupOverlay() {
  if (selectionBox && document.body.contains(selectionBox)) {
    document.body.removeChild(selectionBox);
  }
  if (overlay && document.body.contains(overlay)) {
    document.body.removeChild(overlay);
  }
  window.screenshotOverlayInjected = false;
  selectionBox = null;
}

overlay.addEventListener('mousedown', (e) => {
  e.preventDefault();
  e.stopPropagation();

  startX = e.clientX;
  startY = e.clientY;
  console.log('Selection started at:', startX, startY);

  selectionBox = document.createElement('div');
  selectionBox.style.position = 'fixed';
  selectionBox.style.border = '1px solid #007bff';
  selectionBox.style.backgroundColor = 'rgba(0, 123, 255, 0.1)';
  selectionBox.style.boxSizing = 'border-box';
  selectionBox.style.pointerEvents = 'none';
  selectionBox.style.zIndex = '1000000';
  selectionBox.style.left = startX + 'px';
  selectionBox.style.top = startY + 'px';
  document.body.appendChild(selectionBox);
});

overlay.addEventListener('mousemove', (e) => {
  if (!selectionBox) return;

  e.preventDefault();
  e.stopPropagation();

  const width = e.clientX - startX;
  const height = e.clientY - startY;
  selectionBox.style.width = Math.abs(width) + 'px';
  selectionBox.style.height = Math.abs(height) + 'px';
  selectionBox.style.left = (width > 0 ? startX : e.clientX) + 'px';
  selectionBox.style.top = (height > 0 ? startY : e.clientY) + 'px';
});

overlay.addEventListener('mouseup', (e) => {
  if (selectionBox) {
    e.preventDefault();
    e.stopPropagation();

    const rect = selectionBox.getBoundingClientRect();
    console.log('Selection completed:', rect);

    // 确保选择区域有效（最小10x10像素）
    if (rect.width < 10 || rect.height < 10) {
      cleanupOverlay();
      alert('选择区域太小，请重新选择');
      return;
    }

    // 获取精确的选择区域坐标（去除边框影响）
    const dpr = window.devicePixelRatio || 1;
    const scrollX = window.scrollX || window.pageXOffset;
    const scrollY = window.scrollY || window.pageYOffset;

    // 计算精确的裁剪区域，考虑设备像素比和滚动偏移
    const cropArea = {
      left: Math.round((rect.left + scrollX) * dpr),
      top: Math.round((rect.top + scrollY) * dpr),
      width: Math.round(rect.width * dpr),
      height: Math.round(rect.height * dpr)
    };

    console.log('Crop area:', cropArea, 'DPR:', dpr);

    // 清理选择框和遮罩
    document.body.removeChild(selectionBox);
    document.body.removeChild(overlay);
    window.screenshotOverlayInjected = false;

    // 先截取整个可见区域
    chrome.runtime.sendMessage({ action: "captureVisibleTab" }, (response) => {
      console.log('Screenshot response:', response);

      if (!response) {
        alert('截图失败：无响应');
        return;
      }

      if (response.error) {
        alert('截图失败：' + response.error);
        return;
      }

      if (!response.dataUrl) {
        alert('截图失败：未获取到图像数据');
        return;
      }

      console.log('Original image size will be determined after loading...');

      // 在客户端进行高精度裁剪
      const img = new Image();
      img.onload = () => {
        console.log('Original image loaded:', img.width, 'x', img.height);
        console.log('Crop area:', cropArea);

        // 验证裁剪区域是否在图像范围内
        if (cropArea.left < 0 || cropArea.top < 0 ||
            cropArea.left + cropArea.width > img.width ||
            cropArea.top + cropArea.height > img.height) {
          console.warn('Crop area exceeds image bounds, adjusting...');
          cropArea.left = Math.max(0, Math.min(cropArea.left, img.width - cropArea.width));
          cropArea.top = Math.max(0, Math.min(cropArea.top, img.height - cropArea.height));
          cropArea.width = Math.min(cropArea.width, img.width - cropArea.left);
          cropArea.height = Math.min(cropArea.height, img.height - cropArea.top);
        }

        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        // 设置画布尺寸为裁剪区域的实际尺寸
        canvas.width = cropArea.width;
        canvas.height = cropArea.height;

        // 启用高质量图像平滑
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = 'high';

        // 精确裁剪：从原图的指定位置裁剪到新画布
        try {
          ctx.drawImage(
            img,
            cropArea.left,    // 源图像的x坐标
            cropArea.top,     // 源图像的y坐标
            cropArea.width,   // 源图像的宽度
            cropArea.height,  // 源图像的高度
            0,                // 目标画布的x坐标
            0,                // 目标画布的y坐标
            cropArea.width,   // 目标画布的宽度
            cropArea.height   // 目标画布的高度
          );

          // 输出高质量PNG
          const croppedDataUrl = canvas.toDataURL('image/png', 1.0);
          console.log('High quality cropped image created, size:', canvas.width, 'x', canvas.height);

          chrome.runtime.sendMessage({
            action: "openEditor",
            dataUrl: croppedDataUrl
          }, (response) => {
            console.log('openEditor response:', response);
            if (!response || !response.success) {
              alert('打开编辑器失败，请重试');
            }
          });
        } catch (error) {
          console.error('Error during image cropping:', error);
          alert('图像裁剪失败：' + error.message);
        }
      };

      img.onerror = () => {
        console.error('Failed to load screenshot image');
        alert('图像加载失败，请重试');
        cleanupOverlay();
      };

      img.src = response.dataUrl;
    });
  } else {
    cleanupOverlay();
  }
});