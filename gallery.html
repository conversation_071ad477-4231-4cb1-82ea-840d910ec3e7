<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>截图相册</title>
  <style>
    body {
      font-family: sans-serif;
      background: #f8f9fa;
      margin: 0;
      padding: 20px;
    }
    h1 {
      text-align: center;
      color: #333;
      margin-bottom: 30px;
    }
    #controls {
      text-align: center;
      margin-bottom: 30px;
    }
    #controls button {
      margin: 0 10px;
      padding: 12px 24px;
      background: #007bff;
      color: white;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
    }
    #controls button:hover {
      background: #0056b3;
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
    }
    #controls button:active {
      transform: translateY(0);
    }
    #controls button#clear-all {
      background: #dc3545;
    }
    #controls button#clear-all:hover {
      background: #c82333;
    }
    #gallery {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      justify-content: center;
    }
    .screenshot-item {
      border: 1px solid #dee2e6;
      border-radius: 8px;
      padding: 15px;
      text-align: center;
      background: white;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      transition: transform 0.2s ease;
    }
    .screenshot-item:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }
    .screenshot-item img {
      max-width: 300px;
      max-height: 200px;
      display: block;
      margin-bottom: 15px;
      border-radius: 4px;
    }
    .screenshot-item .actions button {
      margin: 0 5px;
      padding: 8px 16px;
      background: #007bff;
      color: white;
      border: none;
      border-radius: 4px;
      font-size: 12px;
      cursor: pointer;
      transition: all 0.2s ease;
    }
    .screenshot-item .actions button:hover {
      background: #0056b3;
    }
    .screenshot-item .actions button.delete {
      background: #dc3545;
    }
    .screenshot-item .actions button.delete:hover {
      background: #c82333;
    }
  </style>
</head>
<body>
  <h1>截图相册</h1>
  <div id="controls">
      <button id="download-all">全部下载 (ZIP)</button>
      <button id="clear-all">清空相册</button>
  </div>
  <div id="gallery"></div>

  <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
  <script src="gallery.js"></script>
</body>
</html>